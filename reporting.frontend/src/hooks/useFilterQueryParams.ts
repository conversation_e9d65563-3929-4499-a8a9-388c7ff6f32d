import { StringParam, useQueryParams, withDefault, createEnumParam, NumberParam } from "use-query-params";

import {
  CardTypes,
  TransactionTypes,
  default_ACH_CC_filterSettings,
  ECheckTransactionsApprovalStatuses,
  ECheckSettlementApprovalStatuses,
  CreditCardAuthorizationsApprovalStatuses,
  CreditCardSettlementApprovalStatuses,
  default_ChargebackReportFilterSettings,
  default_ChargebackFilterSettings,
} from "../common/entities";

export const CardTypeParam = createEnumParam([CardTypes.ACH, CardTypes.AX, CardTypes.DS, CardTypes.MC, CardTypes.VS]);
export const ApprovalStatusParam = createEnumParam([
  //ECheckTransactionsApprovalStatuses
  ECheckTransactionsApprovalStatuses.CHECK_ACCEPTED,
  ECheckTransactionsApprovalStatuses.DECLINED,
  //ECheckSettlementApprovalStatuses
  ECheckSettlementApprovalStatuses.SETTLED,
  ECheckSettlementApprovalStatuses.REJECTED,
  //CreditCardAuthorizationsApprovalStatuses
  CreditCardAuthorizationsApprovalStatuses.APPROVED,
  CreditCardAuthorizationsApprovalStatuses.DECLINED,
  //CreditCardSettlementApprovalStatuses
  CreditCardSettlementApprovalStatuses.SETTLED,
]);
export const TransactionTypeParam = createEnumParam([
  TransactionTypes.AUTH_ONLY,
  TransactionTypes.CAPT,
  TransactionTypes.SALE,
  TransactionTypes.REFUND,
  TransactionTypes.VOID,
  TransactionTypes.CHECK,
  TransactionTypes.TOKENIZE,
]);

export const useFilterQueryParams = () => {
  const [filterQueryParams, setFilterQueryParams] = useQueryParams({
    merchantId: withDefault(StringParam, default_ACH_CC_filterSettings.merchantId),
    achMerchantId: withDefault(StringParam, default_ACH_CC_filterSettings.achMerchantId),
    ccMerchantId: withDefault(StringParam, default_ACH_CC_filterSettings.ccMerchantId),
    fromDate: withDefault(StringParam, default_ACH_CC_filterSettings.fromDate),
    toDate: withDefault(StringParam, default_ACH_CC_filterSettings.toDate),
    cardType: withDefault(CardTypeParam, default_ACH_CC_filterSettings.cardType),
    approvalStatus: withDefault(ApprovalStatusParam, default_ACH_CC_filterSettings.approvalStatus),
    terminalId: withDefault(StringParam, default_ACH_CC_filterSettings.terminalId),
    transactionId: withDefault(StringParam, default_ACH_CC_filterSettings.transactionId),
    cardNumber: withDefault(StringParam, default_ACH_CC_filterSettings.cardNumber),
    cardHolder: withDefault(StringParam, default_ACH_CC_filterSettings.cardHolder),
    authCode: withDefault(StringParam, default_ACH_CC_filterSettings.authCode),
    ud1: withDefault(StringParam, default_ACH_CC_filterSettings.ud1),
    ud2: withDefault(StringParam, default_ACH_CC_filterSettings.ud2),
    ud3: withDefault(StringParam, default_ACH_CC_filterSettings.ud3),
    orderNumber: withDefault(StringParam, default_ACH_CC_filterSettings.orderNumber),
    transactionType: withDefault(TransactionTypeParam, default_ACH_CC_filterSettings.transactionType),
    amount: withDefault(StringParam, default_ACH_CC_filterSettings.amount),
    settleToDate: withDefault(StringParam, default_ACH_CC_filterSettings.settleToDate),
    settleFromDate: withDefault(StringParam, default_ACH_CC_filterSettings.settleFromDate),
    funding: withDefault(StringParam, default_ACH_CC_filterSettings.funding),
    stType: withDefault(StringParam, default_ACH_CC_filterSettings.stType),
    achStType: withDefault(StringParam, default_ACH_CC_filterSettings.achStType),
    status: withDefault(StringParam, default_ACH_CC_filterSettings.status),
    activeStatus: withDefault(StringParam, default_ACH_CC_filterSettings.activeStatus),
    ipTransactionID: withDefault(StringParam, default_ACH_CC_filterSettings.ipTransactionID),
    reportType: withDefault(StringParam, default_ACH_CC_filterSettings.reportType),
    subType: withDefault(StringParam, default_ACH_CC_filterSettings.subType),
    authStartDateTime: withDefault(StringParam, default_ACH_CC_filterSettings.authStartDateTime),
    authEndDateTime: withDefault(StringParam, default_ACH_CC_filterSettings.authEndDateTime),
    bankId: withDefault(NumberParam, default_ChargebackReportFilterSettings.bankId),
    year: withDefault(NumberParam, default_ChargebackReportFilterSettings.year),
    month: withDefault(NumberParam, default_ChargebackReportFilterSettings.month),
    // Chargeback-specific parameters
    merchantName: withDefault(StringParam, default_ChargebackFilterSettings.merchantName),
    caseNumber: withDefault(StringParam, default_ChargebackFilterSettings.caseNumber),
    creditCard: withDefault(StringParam, default_ChargebackFilterSettings.creditCard),
    reason: withDefault(StringParam, default_ChargebackFilterSettings.reason),
    resolutionTo: withDefault(StringParam, default_ChargebackFilterSettings.resolutionTo),
    debitCredit: withDefault(StringParam, default_ChargebackFilterSettings.debitCredit),
    type: withDefault(StringParam, default_ChargebackFilterSettings.type),
    originRef: withDefault(StringParam, default_ChargebackFilterSettings.originRef),
    arn: withDefault(StringParam, default_ChargebackFilterSettings.arn),
  });

  const replaceFilterQueryParam = (key: string, value: string | number | undefined | null) => {
    setFilterQueryParams(
      {
        [key]: value,
      },
      "replaceIn"
    );
  };

  const resetAllFilterQueryParams = () => {
    setFilterQueryParams(
      {
        merchantId: undefined,
        achMerchantId: undefined,
        ccMerchantId: undefined,
        fromDate: undefined,
        toDate: undefined,
        cardType: undefined,
        approvalStatus: undefined,
        terminalId: undefined,
        transactionId: undefined,
        cardNumber: undefined,
        cardHolder: undefined,
        authCode: undefined,
        ud1: undefined,
        ud2: undefined,
        ud3: undefined,
        orderNumber: undefined,
        transactionType: undefined,
        amount: undefined,
        funding: undefined,
        stType: undefined,
        achStType: undefined,
        status: undefined,
        activeStatus: undefined,
        // Reset chargeback-specific parameters
        merchantName: undefined,
        caseNumber: undefined,
        creditCard: undefined,
        reason: undefined,
        resolutionTo: undefined,
        debitCredit: undefined,
        type: undefined,
        originRef: undefined,
        arn: undefined,
      },
      "replaceIn"
    );
  };

  const resetAllAdditionalFilterQueryParams = () => {
    setFilterQueryParams(
      {
        cardType: undefined,
        approvalStatus: undefined,
        terminalId: undefined,
        transactionId: undefined,
        cardNumber: undefined,
        cardHolder: undefined,
        authCode: undefined,
        ud1: undefined,
        ud2: undefined,
        ud3: undefined,
        orderNumber: undefined,
        transactionType: undefined,
        amount: undefined,
        funding: undefined,
        stType: undefined,
        achStType: undefined,
        status: undefined,
        // Reset chargeback-specific additional parameters
        merchantName: undefined,
        caseNumber: undefined,
        creditCard: undefined,
        reason: undefined,
        resolutionTo: undefined,
        debitCredit: undefined,
        type: undefined,
        originRef: undefined,
        arn: undefined,
      },
      "replaceIn"
    );
  };

  return {
    filterQueryParams,
    setFilterQueryParams,
    replaceFilterQueryParam,
    resetAllFilterQueryParams,
    resetAllAdditionalFilterQueryParams,
  };
};
