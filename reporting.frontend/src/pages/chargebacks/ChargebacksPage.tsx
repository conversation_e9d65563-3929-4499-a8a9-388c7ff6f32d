import React, { use<PERSON>allback, useEffect, useMemo } from "react";

import Link from "@material-ui/core/Link";
import { GridCellParams } from "@material-ui/x-grid";
import moment from "moment-timezone";

import { ChargebackFilterSettings } from "../../common/entities";
import { Breadcrumbs } from "../../components/Breadcrumbs";
import { ExportButtonLink } from "../../components/buttons/ExportButton";
import { PageTitle } from "../../components/PageTitle";
import { AdvancedTable } from "../../components/tables/AdvancedTable";
import { ChargebackFilter } from "../../components/chargeback-filter/ChargebackFilter";
import { ChargebackApi } from "../../domain/chargeback/api/chargeback-api";
import {
  CHARGEBACKS_TABLE_COLS,
  CHARGEBACKS_TABLE_COLS_ADDITIONAL,
} from "../../domain/chargeback/const/chargeback.table.const";
import { AvailableUserSettingsTypes, UserSettingsDto } from "../../domain/user-config/api/user-config-api";
import { useFetch } from "../../hooks/use_ACH_CC_DataQuery";
import { useChargebackFilterSettings } from "../../hooks/useChargebackFilterSettings";
import { DashboardRouteNames } from "../../routes/constants";
import { CT_TIMEZONE, DATE_ONLY_FORMAT } from "../../utils/dateString";

import { ActionButtonsContainer } from "../../styles/Global.styles";

const breadcrumbs = [
  {
    to: DashboardRouteNames.ROOT,
    label: "Dashboard",
  },
  {
    label: "Chargebacks",
  },
];

const ChargebacksPage: React.FC = () => {
  const tableSettingsParams = useMemo(() => {
    return {
      tableType: AvailableUserSettingsTypes.CHARGEBACKS_TABLE,
      tableColumns: CHARGEBACKS_TABLE_COLS,
    };
  }, []);

  const { filterSettings, onResetFilter, onResetDrawerFilter, onSubmit } = useChargebackFilterSettings();

  const dataRequestParams = useMemo(() => {
    return {
      request: ChargebackApi.getChargebacks,
      requestParams: {
        ...filterSettings,
        merchantId: filterSettings.merchantId as string,
        fromDate: filterSettings.fromDate as string,
        toDate: filterSettings.toDate as string,
        merchantList: true,
      },
    };
  }, [filterSettings]);

  const [{ isDataLoading, rowsToDisplay, totalRecords, totalAmount }, fetchData, resetData] = useFetch({
    dataRequestParams,
    tableSettingsParams,
  });

  useEffect(() => {
    if (filterSettings.fromDate && filterSettings.toDate) {
      fetchData();
    }
  }, [fetchData, filterSettings]);

  const handleSubmit = useCallback(
    (settings: ChargebackFilterSettings) => {
      onSubmit(settings);
    },
    [onSubmit]
  );

  const handleResetFilter = useCallback(() => {
    onResetFilter();
    resetData();
  }, [onResetFilter, resetData]);

  const setColumns = useCallback((tableSettings: UserSettingsDto) => {
    const allColumns = [...tableSettings.configs.columns, ...CHARGEBACKS_TABLE_COLS_ADDITIONAL];
    return allColumns.map((c) => {
      if (c.field === "dateTransaction" || c.field === "dateResolved") {
        return {
          ...c,
          renderCell: (p: GridCellParams) => {
            return <span>{moment.tz(p.row[c.field], CT_TIMEZONE).format(DATE_ONLY_FORMAT)}</span>;
          },
        };
      }

      if (c.field === "caseNumber") {
        return {
          ...c,
          renderCell: (p: GridCellParams) => {
            return p.row.reportFileName ? (
              <Link
                href={ChargebackApi.downloadChargeback(p.row.reportFileName)}
                target="_self"
                underline="none"
                style={{ padding: 0, minWidth: 0 }}
              >
                {p.value}
              </Link>
            ) : (
              p.value
            );
          },
        };
      }

      return c;
    });
  }, []);

  return (
    <>
      <PageTitle>Chargebacks</PageTitle>
      <Breadcrumbs crumbs={breadcrumbs} />
      <ChargebackFilter
        disableToolbar
        onSubmit={handleSubmit}
        onResetFilter={handleResetFilter}
        onResetDrawerFilter={onResetDrawerFilter}
        quickJumpDateRequest={ChargebackApi.getDatesByMerchantId}
        optionalMerchantId
        isSearching={isDataLoading}
      />
      <ActionButtonsContainer>
        <ExportButtonLink
          href={ChargebackApi.exportChargebacks({
            ...filterSettings,
            fromDate: filterSettings.fromDate as string,
            toDate: filterSettings.toDate as string,
          })}
          disabled={!(filterSettings.fromDate && filterSettings.toDate) || isDataLoading}
        />
      </ActionButtonsContainer>
      <AdvancedTable
        setColumns={setColumns}
        tableSettingsParams={tableSettingsParams}
        isLoading={isDataLoading}
        rows={rowsToDisplay}
        totalRecords={totalRecords}
        totalAmount={totalAmount}
      />
    </>
  );
};

export default ChargebacksPage;
