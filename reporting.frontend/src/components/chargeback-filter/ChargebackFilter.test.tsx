import React from "react";
import { render, screen } from "@testing-library/react";
import { MuiPickersUtilsProvider } from "@material-ui/pickers";
import MomentUtils from "@date-io/moment";
import { ThemeProvider } from "@material-ui/core/styles";
import { createMuiTheme } from "@material-ui/core";

import { ChargebackFilter } from "./ChargebackFilter";
import { EFilterType } from "../../types/filter.types";

// Mock the hooks
jest.mock("../../hooks/useFilterQueryParams", () => ({
  useFilterQueryParams: () => ({
    filterQueryParams: {},
    setFilterQueryParams: jest.fn(),
    resetAllFilterQueryParams: jest.fn(),
    resetAllAdditionalFilterQueryParams: jest.fn(),
  }),
}));

const theme = createMuiTheme();

const defaultProps = {
  onSubmit: jest.fn(),
  onResetFilter: jest.fn(),
  onResetDrawerFilter: jest.fn(),
  isSearching: false,
};

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <ThemeProvider theme={theme}>
      <MuiPickersUtilsProvider utils={MomentUtils}>
        {component}
      </MuiPickersUtilsProvider>
    </ThemeProvider>
  );
};

describe("ChargebackFilter", () => {
  it("renders without crashing", () => {
    renderWithProviders(<ChargebackFilter {...defaultProps} />);
    expect(screen.getByText("Filter")).toBeInTheDocument();
  });

  it("displays date pickers", () => {
    renderWithProviders(<ChargebackFilter {...defaultProps} />);
    expect(screen.getByLabelText("Date From")).toBeInTheDocument();
    expect(screen.getByLabelText("Date To")).toBeInTheDocument();
  });

  it("displays search and reset buttons", () => {
    renderWithProviders(<ChargebackFilter {...defaultProps} />);
    expect(screen.getByRole("button", { name: "Search" })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Reset" })).toBeInTheDocument();
  });

  it("disables search button when required fields are missing", () => {
    renderWithProviders(<ChargebackFilter {...defaultProps} />);
    const searchButton = screen.getByRole("button", { name: "Search" });
    expect(searchButton).toBeDisabled();
  });
});
