import { axiosPrivateInstance } from "../../../api/axios-config";
import { PaginatedResponse, PaginationQueryParams } from "../../../api/types";
import { typedEnv } from "../../../environments/environment";
import { createQueryString } from "../../../utils/createQueryString";

export interface GetChargebackParams extends PaginationQueryParams {
  merchantId?: string;
  fromDate: string;
  toDate: string;
}

export interface ExportChargebackParams extends Omit<GetChargebackParams, "page" | "size" | "sort"> {}

// TODO: use 'ChargebackDto' from swagger-types, when it will be correct
export interface Chargeback {
  id: number;
  merchantName: string;
  merchantId: string;
  cardType: string;
  creditCard: string;
  caseNumber: string;
  amount: string;
  reason: string;
  resolutionTo: string;
  debitCredit: string;
  type: string;
  originRef: string;
  dateTransaction: string;
  dateResolved: string;
  reportFileName: string | null;
}

export class ChargebackApi {
  static getChargebacks(params: GetChargebackParams) {
    params.size = params.size || 20;
    params.page = params.page || 0;
    return axiosPrivateInstance.get<PaginatedResponse<Chargeback[]>>(`/chargebacks${createQueryString(params)}`);
  }

  static getDatesByMerchantId(merchantId: string) {
    return axiosPrivateInstance.get<string[]>(`/chargebacks/${merchantId}/dates`);
  }

  static exportChargebacks(params: ExportChargebackParams) {
    return `${typedEnv.REACT_APP_API_URL}/private/chargebacks/export${createQueryString(params)}`;
  }

  static downloadChargeback(fileName: string) {
    return `${typedEnv.REACT_APP_API_URL}/private/files/${fileName}`;
  }
}
